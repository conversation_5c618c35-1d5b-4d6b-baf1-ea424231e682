import {
    createSSRApp
} from "vue"
import App from "./App.vue"
import * as <PERSON><PERSON> from "pinia"
import pushService from '@/utils/push'

console.log(">>>>>>应用运行的模式: ",  process.env.NODE_ENV)  // 环境模式 (development, production ...)
console.log('>>>>>>请求地址:', import.meta.env.VITE_API_BASE_URL)  // 自定义环境变量

export function createApp() {
    const app = createSSRApp(App)
    // app.use(Pinia.createPinia(), uviewPlus)
    app.use(Pinia.createPinia())
    
    // 全局注册推送服务
    app.config.globalProperties.$push = pushService
    
    return {
        app,
        Pinia,
    }
}