# 自定义组件库

本项目已完全移除 uview-plus 依赖，使用自定义组件替代。所有组件都符合项目的设计规范，支持高对比度显示，适配移动端使用。

## 组件列表

### 1. CustomModal - 自定义模态框
**路径**: `src/components/CustomModal/CustomModal.vue`

**功能**: 替代 `up-modal`，提供模态对话框功能

**基础用法**:
```vue
<CustomModal 
  :show="showModal" 
  title="提示"
  @cancel="showModal = false"
  @confirm="handleConfirm"
>
  <view>模态框内容</view>
</CustomModal>
```

**主要属性**:
- `show`: 是否显示
- `title`: 标题
- `showCancelButton`: 是否显示取消按钮
- `buttonText`: 按钮文字配置
- `confirmDisabled`: 是否禁用确认按钮

### 2. CustomPopup - 自定义弹出层
**路径**: `src/components/CustomPopup/CustomPopup.vue`

**功能**: 替代 `up-popup`，提供多种弹出模式

**基础用法**:
```vue
<CustomPopup 
  :show="showPopup" 
  mode="bottom"
  :scrollable="true"
  @close="showPopup = false"
>
  <view>弹出层内容</view>
</CustomPopup>
```

**主要属性**:
- `show`: 是否显示
- `mode`: 弹出模式（center/bottom/top/left/right）
- `scrollable`: 是否可滚动
- `round`: 圆角设置
- `closeable`: 是否显示关闭按钮

### 3. CustomButton - 自定义按钮
**路径**: `src/components/CustomButton/CustomButton.vue`

**功能**: 替代 `up-button`，提供按钮功能

**基础用法**:
```vue
<CustomButton 
  type="primary" 
  text="确认"
  @click="handleClick"
/>
```

**主要属性**:
- `type`: 按钮类型（default/primary/success/warning/error）
- `size`: 按钮尺寸（mini/small/normal/large）
- `text`: 按钮文字
- `disabled`: 是否禁用
- `loading`: 是否加载中

### 4. CustomInput - 自定义输入框
**路径**: `src/components/CustomInput/CustomInput.vue`

**功能**: 替代 `up-input`，提供输入框功能

**基础用法**:
```vue
<CustomInput 
  v-model="inputValue"
  placeholder="请输入内容"
  border="surround"
/>
```

**主要属性**:
- `modelValue`: 输入值
- `type`: 输入类型（text/number/password等）
- `border`: 边框样式（surround/bottom/none）
- `clearable`: 是否可清除
- `disabled`: 是否禁用

### 5. CustomRadio - 自定义单选框
**路径**: `src/components/CustomRadio/CustomRadio.vue`

**功能**: 替代 `up-radio-group` 和 `up-radio`，提供单选功能

**基础用法**:
```vue
<CustomRadio 
  v-model="selectedValue"
  :options="radioOptions"
  placement="row"
/>
```

**选项格式**:
```javascript
const radioOptions = [
  { value: 1, label: '选项1' },
  { value: 2, label: '选项2' }
]
```

**主要属性**:
- `modelValue`: 选中值
- `options`: 选项数组
- `placement`: 布局方式（row/column）
- `shape`: 形状（round/square）

### 6. CustomNumberBox - 自定义数字输入框
**路径**: `src/components/CustomNumberBox/CustomNumberBox.vue`

**功能**: 替代 `u-number-box`，提供数字输入功能

**基础用法**:
```vue
<CustomNumberBox
  v-model="numberValue"
  :min="0"
  :max="100"
  :integer="true"
/>
```

**主要属性**:
- `modelValue`: 数字值
- `min`: 最小值
- `max`: 最大值
- `step`: 步长
- `integer`: 是否只允许整数

### 7. CustomEmpty - 自定义空状态
**路径**: `src/components/CustomEmpty/CustomEmpty.vue`

**功能**: 替代 `up-empty`，提供空状态展示

**基础用法**:
```vue
<CustomEmpty
  mode="list"
  title="暂无数据"
  description="当前没有可显示的数据"
  actionText="重新加载"
  @action="handleRetry"
/>
```

**主要属性**:
- `mode`: 预设模式（data/list/search/network/permission/cart）
- `icon`: 自定义图标路径
- `title`: 标题文字
- `description`: 描述文字
- `actionText`: 操作按钮文字

### 8. CustomForm & CustomFormItem - 自定义表单
**路径**: `src/components/CustomForm/CustomForm.vue`、`src/components/CustomFormItem/CustomFormItem.vue`

**功能**: 替代 `up-form` 和 `up-form-item`，提供表单功能

**基础用法**:
```vue
<CustomForm ref="formRef" :model="formData" :rules="rules">
  <CustomFormItem label="用户名" prop="username">
    <CustomInput v-model="formData.username" placeholder="请输入用户名" />
  </CustomFormItem>
</CustomForm>
```

**主要属性**:
- `model`: 表单数据对象
- `rules`: 验证规则
- `labelPosition`: 标签位置（left/top/right）
- `labelWidth`: 标签宽度

### 9. CustomTextarea - 自定义文本域
**路径**: `src/components/CustomTextarea/CustomTextarea.vue`

**功能**: 替代 `up-textarea`，提供多行文本输入

**基础用法**:
```vue
<CustomTextarea
  v-model="textValue"
  placeholder="请输入内容"
  :rows="4"
  :showWordLimit="true"
  :maxlength="200"
/>
```

**主要属性**:
- `modelValue`: 文本值
- `rows`: 行数
- `maxlength`: 最大长度
- `showWordLimit`: 是否显示字数统计

### 10. CustomPicker - 自定义选择器
**路径**: `src/components/CustomPicker/CustomPicker.vue`

**功能**: 替代 `up-picker`，提供选择器功能

**基础用法**:
```vue
<CustomPicker
  :show="showPicker"
  title="请选择"
  :columns="pickerColumns"
  @confirm="handlePickerConfirm"
  @cancel="showPicker = false"
/>
```

**主要属性**:
- `show`: 是否显示
- `columns`: 选择器数据
- `title`: 标题
- `keyName`: 显示字段名
- `valueName`: 值字段名

## 设计规范

### 颜色方案
- **主色调**: `#1890ff` (蓝色)
- **成功色**: `#52c41a` (绿色)
- **警告色**: `#faad14` (橙色)
- **错误色**: `#ff4d4f` (红色)
- **文字色**: `#000` (黑色，高对比度)
- **辅助文字**: `#666` (深灰色)
- **边框色**: `#d9d9d9` (浅灰色)

### 尺寸规范
- **圆角**: `8rpx` (默认), `12rpx` (卡片), `24rpx` (大圆角)
- **间距**: `20rpx` (小), `30rpx` (中), `40rpx` (大)
- **字体**: `26rpx` (小), `28rpx` (默认), `30rpx` (大), `32rpx` (标题)
- **按钮高度**: `60rpx` (小), `80rpx` (默认), `100rpx` (大)

### 动画效果
- **过渡时间**: `0.3s`
- **缓动函数**: `ease-out`
- **变换**: 支持淡入淡出、滑动、缩放等效果

## 迁移指南

### 从 uview-plus 迁移

1. **移除 uview-plus 依赖**
   ```bash
   npm uninstall uview-plus
   ```

2. **更新 pages.json**
   ```json
   "easycom": {
     "autoscan": true,
     "custom": {
       // 注释掉 uview-plus 相关配置
     }
   }
   ```

3. **组件替换对照表**
   | uview-plus | 自定义组件 | 说明 |
   |------------|------------|------|
   | up-modal | CustomModal | 模态框 |
   | up-popup | CustomPopup | 弹出层 |
   | up-button | CustomButton | 按钮 |
   | up-input | CustomInput | 输入框 |
   | up-radio-group | CustomRadio | 单选框组 |
   | u-number-box | CustomNumberBox | 数字输入框 |
   | up-empty | CustomEmpty | 空状态 |
   | up-form | CustomForm | 表单容器 |
   | up-form-item | CustomFormItem | 表单项 |
   | up-textarea | CustomTextarea | 文本域 |
   | up-picker | CustomPicker | 选择器 |

4. **属性名变化**
   - 数值属性需要使用 v-bind: `:round="32"` 而不是 `round="32"`
   - 驼峰命名: `:maxHeight` 而不是 `:max-height`
   - 布尔属性: `:disabled="true"` 而不是 `disabled`

## 注意事项

1. **导入组件**: 确保正确导入所需的自定义组件
2. **属性绑定**: 注意使用 v-bind 绑定数值和布尔属性
3. **事件处理**: 事件名称保持一致，但确保正确处理
4. **样式覆盖**: 使用深度选择器 `:deep()` 来自定义样式
5. **响应式**: 所有组件都支持响应式设计，适配不同屏幕尺寸

## 开发建议

1. **统一风格**: 使用统一的设计规范和颜色方案
2. **高对比度**: 确保文字和背景有足够的对比度
3. **移动优先**: 优先考虑移动端的使用体验
4. **性能优化**: 避免不必要的重渲染和计算
5. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展开发

如需添加新的自定义组件：

1. 在 `src/components/` 下创建组件目录
2. 遵循现有的命名规范和代码结构
3. 添加完整的 props 定义和事件处理
4. 实现响应式设计和高对比度支持
5. 编写使用文档和示例代码
