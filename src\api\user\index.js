import request from '@/utils/request'

// api地址
const api = {
    info: '/api/v1/web/account/current-account-detail',
    list: '/app-api/user/list',
    correctionList: "/app-api/user/correction/list",
    // 整改任务详情
    correctionDetail: "/app-api/user/correction/detail",
    // 注销用户
    delete: "/app-api/user/delete",
    // 获取用户底部角标数据
    userBadge: "/app-api/user/badge"

}

// 获取用户信息
export function getUserInfo() {
    return request.get(api.info)
}

// 用户列表
export function getUserListByHospitalId(hospitalId) {
    return request.get(api.list + '/' + hospitalId)
}

// 创建的整改列表
export function getCorrectionList(params = {}) {
    // 处理请求参数
    const requestParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10
    }

    // 只有当状态不是"全部"时才添加状态参数
    if (params.status !== -1) {
        requestParams.status = params.status
    }

    return request.get(api.correctionList, params, {load: false})
}

// 任务详情
export function getCorrectionDetail(tasksId) {
    return request.get(api.correctionDetail, {tasksId: tasksId})
}

// 注销用户
export function deleteAccount() {
    return request.post(api.delete)
}

// 用户角标数据
export function getUserBadgeCount() {
    return request.get(api.userBadge, null, {loading: false})
}
