<template>
  <view v-if="show" class="modal-overlay" @click="handleOverlayClick" @touchmove.stop.prevent="noop">
    <view class="modal-container" @click.stop @touchmove.stop>
      <!-- 标题 -->
      <view class="modal-header" v-if="title">
        <text class="modal-title">{{ title }}</text>
      </view>
      
      <!-- 内容 -->
      <view class="modal-content">
        <slot></slot>
      </view>
      
      <!-- 按钮组 -->
      <view class="modal-footer">
        <button 
          v-if="showCancelButton" 
          class="modal-btn cancel-btn" 
          @click="handleCancel"
        >
          {{ buttonText.cancel || '取消' }}
        </button>
        <button 
          class="modal-btn confirm-btn" 
          :class="{ disabled: confirmDisabled }"
          :disabled="confirmDisabled"
          @click="handleConfirm"
        >
          {{ buttonText.confirm || '确定' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { watch, onUnmounted } from 'vue'
// Vue 3 编译器宏，无需导入

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  showCancelButton: {
    type: Boolean,
    default: true
  },
  buttonText: {
    type: Object,
    default: () => ({
      cancel: '取消',
      confirm: '确定'
    })
  },
  confirmDisabled: {
    type: Boolean,
    default: false
  },
  closeOnClickOverlay: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['cancel', 'confirm', 'close'])

const handleCancel = () => {
  emit('cancel')
  emit('close')
}

const handleConfirm = () => {
  if (!props.confirmDisabled) {
    emit('confirm')
  }
}

const handleOverlayClick = () => {
  if (props.closeOnClickOverlay) {
    emit('cancel')
    emit('close')
  }
}

// 空函数，用于阻止事件
const noop = () => {}

// 禁止页面滚动的函数
const preventPageScroll = () => {
  // 获取当前滚动位置
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop

  // 保存当前滚动位置
  document.body.style.position = 'fixed'
  document.body.style.top = `-${scrollTop}px`
  document.body.style.width = '100%'
  document.body.style.overflow = 'hidden'
}

// 恢复页面滚动的函数
const restorePageScroll = () => {
  const scrollTop = Math.abs(parseInt(document.body.style.top || '0'))

  // 恢复页面滚动
  document.body.style.position = ''
  document.body.style.top = ''
  document.body.style.width = ''
  document.body.style.overflow = ''

  // 恢复滚动位置
  if (scrollTop > 0) {
    document.documentElement.scrollTop = scrollTop
    document.body.scrollTop = scrollTop
  }
}

// 监听弹窗显示状态变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 弹窗打开时禁止页面滚动
    // #ifdef H5
    preventPageScroll()
    // #endif
  } else {
    // 弹窗关闭时恢复页面滚动
    // #ifdef H5
    restorePageScroll()
    // #endif
  }
}, { immediate: true })

// 组件卸载时恢复页面滚动
onUnmounted(() => {
  // #ifdef H5
  if (props.show) {
    restorePageScroll()
  }
  // #endif
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
  box-sizing: border-box;
}

.modal-container {
  background: white;
  border-radius: 12rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  text-align: center;
}

.modal-content {
  padding: 20rpx 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  font-size: 30rpx;
  font-weight: 500;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.cancel-btn:active {
  background: #f5f5f5;
}

.confirm-btn {
  color: #1890ff;
}

.confirm-btn:active {
  background: #f0f8ff;
}

.confirm-btn.disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

.confirm-btn.disabled:active {
  background: none;
}

/* 只有一个按钮时的样式 */
.modal-footer:has(.modal-btn:only-child) .modal-btn {
  border-right: none;
}
</style>
