# CustomEmpty 自定义空状态组件

## 概述

`CustomEmpty` 是一个自定义的空状态组件，用于替换 uview-plus 的 `up-empty` 组件。它提供了多种预设模式和高度可定制的空状态展示功能。

## 功能特性

- ✅ 支持多种预设模式（data、list、search、network、permission、cart）
- ✅ 支持自定义图标和图片
- ✅ 支持自定义标题和描述文字
- ✅ 支持操作按钮和自定义操作
- ✅ 支持自定义尺寸和间距
- ✅ 流畅的动画效果
- ✅ 响应式设计
- ✅ 符合项目设计规范

## 基础用法

### 1. 导入组件

```vue
<script setup>
import CustomEmpty from '@/components/CustomEmpty/CustomEmpty.vue'
</script>
```

### 2. 基础空状态

```vue
<template>
  <CustomEmpty 
    mode="data" 
    title="暂无数据"
    description="当前没有可显示的数据"
  />
</template>
```

### 3. 带操作按钮的空状态

```vue
<template>
  <CustomEmpty 
    mode="network" 
    title="网络异常"
    description="请检查网络连接后重试"
    actionText="重新加载"
    @action="handleRetry"
  />
</template>

<script setup>
const handleRetry = () => {
  console.log('重新加载数据')
  // 执行重新加载逻辑
}
</script>
```

## API 参数

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| mode | String | 'data' | 预设模式：data/list/search/network/permission/cart |
| icon | String | '' | 自定义图标/图片路径 |
| title | String | '' | 标题文字（为空时使用预设标题） |
| description | String | '' | 描述文字（为空时使用预设描述） |
| actionText | String | '' | 操作按钮文字 |
| actionDisabled | Boolean | false | 是否禁用操作按钮 |
| showAction | Boolean | true | 是否显示操作区域 |
| width | String | '100%' | 组件宽度 |
| height | String | 'auto' | 组件高度 |
| marginTop | String | '100rpx' | 顶部间距 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| action | 点击操作按钮时触发 | - |
| image-error | 图片加载失败时触发 | error |

### Slots

| 插槽名 | 说明 |
|--------|------|
| action | 自定义操作区域内容 |

## 预设模式

### 1. data - 数据为空
```vue
<CustomEmpty mode="data" />
```
- 图标：📄
- 标题：暂无数据
- 描述：当前没有可显示的数据

### 2. list - 列表为空
```vue
<CustomEmpty mode="list" />
```
- 图标：📋
- 标题：列表为空
- 描述：当前列表没有任何内容

### 3. search - 搜索无结果
```vue
<CustomEmpty mode="search" />
```
- 图标：🔍
- 标题：搜索无结果
- 描述：试试其他关键词吧

### 4. network - 网络异常
```vue
<CustomEmpty mode="network" />
```
- 图标：🌐
- 标题：网络异常
- 描述：请检查网络连接后重试

### 5. permission - 权限不足
```vue
<CustomEmpty mode="permission" />
```
- 图标：🔒
- 标题：暂无权限
- 描述：您没有访问权限

### 6. cart - 购物车为空
```vue
<CustomEmpty mode="cart" />
```
- 图标：🛒
- 标题：购物车为空
- 描述：快去添加一些商品吧

## 使用示例

### 1. 任务列表为空

```vue
<CustomEmpty 
  mode="list" 
  icon="/static/empty.jpg"
  title="暂无任务" 
  description="当前没有符合条件的任务"
/>
```

### 2. 网络错误重试

```vue
<CustomEmpty 
  mode="network" 
  title="加载失败"
  description="网络连接异常，请重试"
  actionText="重新加载"
  @action="reloadData"
/>
```

### 3. 搜索无结果

```vue
<CustomEmpty 
  mode="search" 
  title="未找到相关内容"
  description="试试调整搜索关键词"
  actionText="清空搜索"
  @action="clearSearch"
/>
```

### 4. 自定义操作区域

```vue
<CustomEmpty mode="data" title="暂无数据">
  <template #action>
    <view class="custom-actions">
      <button class="action-btn primary" @click="addData">添加数据</button>
      <button class="action-btn secondary" @click="importData">导入数据</button>
    </view>
  </template>
</CustomEmpty>
```

### 5. 权限不足

```vue
<CustomEmpty 
  mode="permission" 
  title="访问受限"
  description="您没有查看此内容的权限，请联系管理员"
  actionText="申请权限"
  @action="requestPermission"
/>
```

## 迁移指南

### 从 up-empty 迁移

**原代码：**
```vue
<up-empty mode="list" icon="/static/empty.jpg" />
```

**新代码：**
```vue
<CustomEmpty 
  mode="list" 
  icon="/static/empty.jpg" 
  title="暂无数据" 
  description="当前没有可显示的数据" 
/>
```

### 主要变化

1. **属性增强**：新增了更多自定义属性
2. **预设模式**：提供了更多预设的空状态模式
3. **操作支持**：内置了操作按钮和自定义操作区域
4. **动画效果**：添加了淡入动画效果

## 样式定制

组件使用了项目统一的设计规范：

- **图标尺寸**：200rpx × 200rpx（移动端 240rpx × 240rpx）
- **标题字体**：32rpx（移动端 34rpx），颜色 #000
- **描述字体**：26rpx（移动端 28rpx），颜色 #666
- **按钮样式**：蓝色主题，圆角 8rpx
- **动画效果**：0.6s 淡入上移动画

### 自定义样式

```vue
<style>
:deep(.custom-empty) {
  padding: 80rpx 40rpx;
}

:deep(.empty-title) {
  color: #ff4757;
}

:deep(.empty-btn) {
  background: #2ed573;
}
</style>
```

## 注意事项

1. **图片路径**：确保自定义图标路径正确
2. **响应式**：组件会根据屏幕尺寸自动调整
3. **插槽使用**：使用 `#action` 插槽可以完全自定义操作区域
4. **事件处理**：记得处理 `@action` 事件
5. **模式选择**：根据实际场景选择合适的预设模式

## 最佳实践

1. **统一风格**：在同一项目中保持空状态的视觉风格一致
2. **有意义的文案**：提供清晰、有帮助的标题和描述
3. **合适的操作**：根据场景提供合适的操作按钮
4. **图片优化**：使用适当尺寸的图片，避免过大影响性能
5. **用户引导**：通过空状态引导用户进行下一步操作
