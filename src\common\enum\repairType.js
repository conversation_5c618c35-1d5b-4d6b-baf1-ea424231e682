// 报修类型枚举
export const REPAIR_TYPES = [
  {
    label: '水系统',
    value: 1
  },
  {
    label: '强电系统',
    value: 2
  },
  {
    label: '弱电系统',
    value: 3
  },
  {
    label: '气路系统',
    value: 4
  },
  {
    label: '工程质量投诉',
    value: 5
  },
  {
    label: '暖通系统',
    value: 7
  },
  {
    label: '建筑系统',
    value: 8
  },
  {
    label: '消防系统',
    value: 9
  },
  {
    label: '其他',
    value: 6
  }
]

// 报修状态枚举
export const REPAIR_STATUS = {
  UNASSIGNED: 1,    // 未指派
  ASSIGNED: 2,      // 已指派
  UNFINISHED: 3,    // 未完成
  PENDING: 4,       // 待审核
  COMPLETED: 5      // 已完成
}

// 报修状态标签
export const REPAIR_STATUS_LABELS = {
  [REPAIR_STATUS.UNASSIGNED]: '未指派',
  [REPAIR_STATUS.ASSIGNED]: '已指派',
  [REPAIR_STATUS.UNFINISHED]: '未完成',
  [REPAIR_STATUS.PENDING]: '待审核',
  [REPAIR_STATUS.COMPLETED]: '已完成'
}

// 根据报修类型值获取标签
export function getRepairTypeLabel(typeValue) {
  const type = REPAIR_TYPES.find(item => item.value === typeValue)
  return type ? type.label : ''
} 