import request from '@/utils/request'

// api地址
const api = {
	login: '/api/v1/web/login',
}

// 用户登录 (手机号+密码+院区ID)
export function login(username, password, hospitalId) {
	const data = {
		phoneNumber: username,
		password: password,
		hospitalId: hospitalId  // 新增院区ID字段
	}
	return request.post(api.login, data, { load: false, })
}


// 微信登陆
export function wxLogin(code) {
	return request.post(api.login, { code: code })
}


// 测试登陆
export function loginTest(mobile = '') {
	if (mobile == "") {
		mobile = "13888888888"
	}
	return request.get('api/v1/consumer/login/test', { phoneNumber: mobile })
}