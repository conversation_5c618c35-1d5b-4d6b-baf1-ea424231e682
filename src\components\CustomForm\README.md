# CustomForm & CustomFormItem 自定义表单组件

## 概述

`CustomForm` 和 `CustomFormItem` 是一套自定义的表单组件，用于替换 uview-plus 的 `up-form` 和 `up-form-item` 组件。它们提供了完整的表单验证、布局和交互功能。

## 功能特性

- ✅ 支持多种标签位置（left、top、right）
- ✅ 支持表单验证（必填、长度、正则、自定义）
- ✅ 支持表单重置和清除验证
- ✅ 支持禁用状态
- ✅ 支持自定义标签宽度
- ✅ 响应式设计
- ✅ 符合项目设计规范

## 基础用法

### 1. 导入组件

```vue
<script setup>
import CustomForm from '@/components/CustomForm/CustomForm.vue'
import CustomFormItem from '@/components/CustomFormItem/CustomFormItem.vue'
import CustomInput from '@/components/CustomInput/CustomInput.vue'
</script>
```

### 2. 基础表单

```vue
<template>
  <CustomForm ref="formRef" :model="formData">
    <CustomFormItem label="用户名" prop="username">
      <CustomInput v-model="formData.username" placeholder="请输入用户名" />
    </CustomFormItem>
    
    <CustomFormItem label="密码" prop="password">
      <CustomInput v-model="formData.password" type="password" placeholder="请输入密码" />
    </CustomFormItem>
    
    <CustomButton @click="handleSubmit">提交</CustomButton>
  </CustomForm>
</template>

<script setup>
import { ref } from 'vue'

const formRef = ref()
const formData = ref({
  username: '',
  password: ''
})

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    console.log('表单验证通过', formData.value)
  } catch (error) {
    console.log('表单验证失败', error.message)
  }
}
</script>
```

## API 参数

### CustomForm Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| model | Object | {} | 表单数据对象 |
| rules | Object | {} | 表单验证规则 |
| labelPosition | String | 'left' | 标签位置：left/top/right |
| labelWidth | String/Number | '200rpx' | 标签宽度 |
| disabled | Boolean | false | 是否禁用整个表单 |

### CustomForm Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| validate | 验证整个表单 | - |
| validateField | 验证指定字段 | prop |
| resetFields | 重置表单 | - |
| clearValidate | 清除验证 | props? |

### CustomFormItem Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| label | String | '' | 标签文字 |
| prop | String | '' | 表单域 model 字段 |
| required | Boolean | false | 是否必填 |
| rules | Array | [] | 表单验证规则 |
| labelWidth | String/Number | '' | 标签宽度（覆盖表单设置） |
| labelPosition | String | '' | 标签位置（覆盖表单设置） |

## 验证规则

### 内置验证规则

```javascript
const rules = {
  username: [
    { required: true, message: '用户名不能为空' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符' }
  ],
  email: [
    { required: true, message: '邮箱不能为空' },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }
  ],
  phone: [
    { required: true, message: '手机号不能为空' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ]
}
```

### 自定义验证函数

```javascript
const rules = {
  password: [
    { required: true, message: '密码不能为空' },
    { 
      validator: (rule, value) => {
        if (value.length < 6) {
          throw new Error('密码长度不能少于6位')
        }
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
          throw new Error('密码必须包含大小写字母和数字')
        }
      }
    }
  ]
}
```

## 使用示例

### 1. 完整的用户注册表单

```vue
<template>
  <CustomForm ref="formRef" :model="formData" :rules="rules" labelPosition="top">
    <CustomFormItem label="用户名" prop="username">
      <CustomInput v-model="formData.username" placeholder="请输入用户名" />
    </CustomFormItem>
    
    <CustomFormItem label="邮箱" prop="email">
      <CustomInput v-model="formData.email" type="email" placeholder="请输入邮箱" />
    </CustomFormItem>
    
    <CustomFormItem label="手机号" prop="phone">
      <CustomInput v-model="formData.phone" type="tel" placeholder="请输入手机号" />
    </CustomFormItem>
    
    <CustomFormItem label="密码" prop="password">
      <CustomInput v-model="formData.password" type="password" placeholder="请输入密码" />
    </CustomFormItem>
    
    <CustomFormItem label="确认密码" prop="confirmPassword">
      <CustomInput v-model="formData.confirmPassword" type="password" placeholder="请确认密码" />
    </CustomFormItem>
    
    <view class="form-actions">
      <CustomButton @click="handleReset">重置</CustomButton>
      <CustomButton type="primary" @click="handleSubmit">注册</CustomButton>
    </view>
  </CustomForm>
</template>

<script setup>
import { ref } from 'vue'

const formRef = ref()
const formData = ref({
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: ''
})

const rules = {
  username: [
    { required: true, message: '用户名不能为空' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符' }
  ],
  email: [
    { required: true, message: '邮箱不能为空' },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }
  ],
  phone: [
    { required: true, message: '手机号不能为空' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ],
  password: [
    { required: true, message: '密码不能为空' },
    { min: 6, message: '密码长度不能少于6位' }
  ],
  confirmPassword: [
    { required: true, message: '确认密码不能为空' },
    { 
      validator: (rule, value) => {
        if (value !== formData.value.password) {
          throw new Error('两次输入的密码不一致')
        }
      }
    }
  ]
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    console.log('注册成功', formData.value)
    uni.showToast({ title: '注册成功', icon: 'success' })
  } catch (error) {
    uni.showToast({ title: error.message, icon: 'none' })
  }
}

const handleReset = () => {
  formRef.value.resetFields()
}
</script>
```

### 2. 水平布局表单

```vue
<template>
  <CustomForm :model="formData" labelPosition="left" labelWidth="150rpx">
    <CustomFormItem label="姓名" prop="name">
      <CustomInput v-model="formData.name" placeholder="请输入姓名" />
    </CustomFormItem>
    
    <CustomFormItem label="年龄" prop="age">
      <CustomNumberBox v-model="formData.age" :min="0" :max="120" />
    </CustomFormItem>
    
    <CustomFormItem label="性别" prop="gender">
      <CustomRadio v-model="formData.gender" :options="genderOptions" placement="row" />
    </CustomFormItem>
  </CustomForm>
</template>
```

### 3. 动态表单验证

```vue
<template>
  <CustomForm ref="formRef" :model="formData">
    <CustomFormItem label="邮箱" prop="email">
      <CustomInput 
        v-model="formData.email" 
        placeholder="请输入邮箱"
        @blur="validateEmail"
      />
    </CustomFormItem>
  </CustomForm>
</template>

<script setup>
const validateEmail = async () => {
  try {
    await formRef.value.validateField('email')
    console.log('邮箱验证通过')
  } catch (error) {
    console.log('邮箱验证失败', error.message)
  }
}
</script>
```

## 迁移指南

### 从 up-form 迁移

**原代码：**
```vue
<up-form ref="formRef" :model="formData" :rules="rules">
  <up-form-item label="用户名" prop="username">
    <up-input v-model="formData.username" placeholder="请输入用户名" />
  </up-form-item>
</up-form>
```

**新代码：**
```vue
<CustomForm ref="formRef" :model="formData" :rules="rules">
  <CustomFormItem label="用户名" prop="username">
    <CustomInput v-model="formData.username" placeholder="请输入用户名" />
  </CustomFormItem>
</CustomForm>
```

### 主要变化

1. **组件名称**：`up-form` → `CustomForm`，`up-form-item` → `CustomFormItem`
2. **验证方法**：方法名保持一致，使用方式相同
3. **样式增强**：更好的响应式设计和视觉效果
4. **验证增强**：更丰富的内置验证规则

## 样式定制

组件使用了项目统一的设计规范：

- **标签字体**：28rpx（移动端 30rpx），颜色 #000
- **必填标记**：红色 #ff4d4f
- **错误信息**：24rpx（移动端 26rpx），颜色 #ff4d4f
- **间距**：30rpx 表单项间距，16rpx 标签间距

### 自定义样式

```vue
<style>
:deep(.custom-form-item) {
  margin-bottom: 40rpx;
}

:deep(.label-text) {
  color: #1890ff;
  font-weight: bold;
}

:deep(.form-item-error-text) {
  background: #fff2f0;
  padding: 8rpx 12rpx;
  border-radius: 4rpx;
}
</style>
```

## 注意事项

1. **表单数据绑定**：确保 `model` 属性正确绑定表单数据对象
2. **验证规则**：`prop` 属性必须与 `model` 中的字段名一致
3. **异步验证**：自定义验证函数支持 Promise 和 async/await
4. **表单重置**：重置表单会清空所有字段值和验证状态
5. **响应式**：组件会根据屏幕尺寸自动调整布局
