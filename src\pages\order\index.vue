<template>
  <view class="mine-container">
    订单
    <!-- 自定义 TabBar -->
    <CustomTabBar ref="customTabBar"/>
  </view>
</template>

<script setup>
import { onMounted, getCurrentInstance } from 'vue'
import { onShow } from "@dcloudio/uni-app"
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import tabBarManager from '@/utils/tabBar'

onMounted(() => {
  // 初始化 tabBarManager
  tabBarManager.init()
})

// 页面显示时更新 TabBar 状态
const updateTabBarIndex = () => {
  // 通过 ref 获取 CustomTabBar 组件实例并更新索引
  const tabBarComponent = getCurrentInstance()?.refs?.customTabBar
  if (tabBarComponent && tabBarComponent.setCurrentIndex) {
    tabBarComponent.setCurrentIndex(2) 
  }
}

onShow(() => {
  // 更新 TabBar 状态
  updateTabBarIndex()
})

</script>

<style scoped>
</style>
