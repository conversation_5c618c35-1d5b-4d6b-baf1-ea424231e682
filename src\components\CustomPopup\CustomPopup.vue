<template>
  <view v-if="show" class="popup-overlay" :style="overlayStyle" @click="handleOverlayClick" @touchmove.stop.prevent="noop">
    <view
      class="popup-container"
      :class="[`popup-${mode}`, { 'popup-round': round }]"
      :style="containerStyle"
      @click.stop
      @touchmove.stop
    >
      <!-- 顶部拖拽条（仅底部弹出时显示） -->
      <view v-if="mode === 'bottom' && showDragBar" class="drag-bar">
        <view class="drag-handle"></view>
      </view>
      
      <!-- 内容区域 -->
      <scroll-view 
        v-if="scrollable" 
        class="popup-content scrollable"
        :style="contentStyle"
        scroll-y
        :enhanced="true"
        :show-scrollbar="false"
      >
        <slot></slot>
      </scroll-view>
      <view v-else class="popup-content" :style="contentStyle">
        <slot></slot>
      </view>
      
      <!-- 关闭按钮 -->
      <view v-if="closeable" class="close-btn" @click="handleClose">
        <text class="close-icon">✕</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'center', // center, bottom, top, left, right
    validator: (value) => ['center', 'bottom', 'top', 'left', 'right'].includes(value)
  },
  round: {
    type: [Boolean, String, Number],
    default: false
  },
  zIndex: {
    type: [String, Number],
    default: 999
  },
  closeable: {
    type: Boolean,
    default: false
  },
  closeOnClickOverlay: {
    type: Boolean,
    default: true
  },
  scrollable: {
    type: Boolean,
    default: false
  },
  showDragBar: {
    type: Boolean,
    default: true
  },
  width: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: ''
  },
  maxHeight: {
    type: String,
    default: '80vh'
  }
})

const emit = defineEmits(['close', 'open'])

// 遮罩层样式
const overlayStyle = computed(() => ({
  zIndex: props.zIndex
}))

// 容器样式
const containerStyle = computed(() => {
  const style = {}
  
  if (props.width) {
    style.width = props.width
  }
  
  if (props.height) {
    style.height = props.height
  }
  
  if (props.round && props.round !== true) {
    style.borderRadius = typeof props.round === 'number' ? `${props.round}rpx` : props.round
  }
  
  return style
})

// 内容区域样式
const contentStyle = computed(() => {
  const style = {}
  
  if (props.scrollable && props.maxHeight) {
    style.maxHeight = props.maxHeight
  }
  
  return style
})

// 处理遮罩层点击
const handleOverlayClick = () => {
  if (props.closeOnClickOverlay) {
    handleClose()
  }
}

// 空函数，用于阻止事件
const noop = () => {}

// 处理关闭
const handleClose = () => {
  emit('close')
}

// 禁止页面滚动的函数
const preventPageScroll = () => {
  // 获取当前滚动位置
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop

  // 保存当前滚动位置
  document.body.style.position = 'fixed'
  document.body.style.top = `-${scrollTop}px`
  document.body.style.width = '100%'
  document.body.style.overflow = 'hidden'
}

// 恢复页面滚动的函数
const restorePageScroll = () => {
  const scrollTop = Math.abs(parseInt(document.body.style.top || '0'))

  // 恢复页面滚动
  document.body.style.position = ''
  document.body.style.top = ''
  document.body.style.width = ''
  document.body.style.overflow = ''

  // 恢复滚动位置
  if (scrollTop > 0) {
    document.documentElement.scrollTop = scrollTop
    document.body.scrollTop = scrollTop
  }
}

// 监听弹窗显示状态变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 弹窗打开时禁止页面滚动
    // #ifdef H5
    preventPageScroll()
    // #endif
  } else {
    // 弹窗关闭时恢复页面滚动
    // #ifdef H5
    restorePageScroll()
    // #endif
  }
}, { immediate: true })

// 组件卸载时恢复页面滚动
onUnmounted(() => {
  // #ifdef H5
  if (props.show) {
    restorePageScroll()
  }
  // #endif
})
</script>

<style scoped>
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  z-index: 999;
}

.popup-container {
  background: white;
  position: relative;
  animation-duration: 0.3s;
  animation-timing-function: ease-out;
  animation-fill-mode: both;
}

/* 居中弹出 */
.popup-center {
  margin: auto;
  border-radius: 12rpx;
  max-width: 90vw;
  max-height: 80vh;
  animation-name: popupFadeIn;
}

/* 底部弹出 */
.popup-bottom {
  align-self: flex-end;
  width: 100%;
  border-radius: 24rpx 24rpx 0 0;
  animation-name: popupSlideUp;
}

/* 顶部弹出 */
.popup-top {
  align-self: flex-start;
  width: 100%;
  border-radius: 0 0 24rpx 24rpx;
  animation-name: popupSlideDown;
}

/* 左侧弹出 */
.popup-left {
  align-self: stretch;
  width: 80vw;
  max-width: 600rpx;
  animation-name: popupSlideRight;
}

/* 右侧弹出 */
.popup-right {
  align-self: stretch;
  margin-left: auto;
  width: 80vw;
  max-width: 600rpx;
  animation-name: popupSlideLeft;
}

/* 圆角样式 */
.popup-round.popup-center {
  border-radius: 24rpx;
}

.popup-round.popup-bottom {
  border-radius: 32rpx 32rpx 0 0;
}

.popup-round.popup-top {
  border-radius: 0 0 32rpx 32rpx;
}

/* 拖拽条 */
.drag-bar {
  display: flex;
  justify-content: center;
  padding: 16rpx 0 8rpx;
}

.drag-handle {
  width: 60rpx;
  height: 6rpx;
  background: #d9d9d9;
  border-radius: 3rpx;
}

/* 内容区域 */
.popup-content {
  flex: 1;
  overflow: hidden;
}

.popup-content.scrollable {
  overflow-y: auto;
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 10;
}

.close-icon {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.2);
}

/* 动画效果 */
@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes popupSlideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes popupSlideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes popupSlideRight {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes popupSlideLeft {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .popup-center {
    max-width: 95vw;
  }
  
  .popup-left,
  .popup-right {
    width: 85vw;
  }
}
</style>
