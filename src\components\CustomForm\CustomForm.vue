<template>
  <view class="custom-form" :class="{ 'form-disabled': disabled }">
    <slot></slot>
  </view>
</template>

<script setup>
import { provide, reactive, ref } from 'vue'

const props = defineProps({
  model: {
    type: Object,
    default: () => ({})
  },
  rules: {
    type: Object,
    default: () => ({})
  },
  labelPosition: {
    type: String,
    default: 'left', // left, top, right
    validator: (value) => ['left', 'top', 'right'].includes(value)
  },
  labelWidth: {
    type: [String, Number],
    default: '200rpx'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// 表单项注册
const formItems = reactive(new Map())

// 提供给子组件的数据
provide('customForm', {
  model: props.model,
  rules: props.rules,
  labelPosition: props.labelPosition,
  labelWidth: props.labelWidth,
  disabled: props.disabled,
  registerItem: (name, item) => {
    formItems.set(name, item)
  },
  unregisterItem: (name) => {
    formItems.delete(name)
  }
})

// 验证单个字段
const validateField = async (prop) => {
  const item = formItems.get(prop)
  if (item && item.validate) {
    return await item.validate()
  }
  return true
}

// 验证所有字段
const validate = async () => {
  const results = []
  for (const [prop, item] of formItems) {
    if (item.validate) {
      try {
        await item.validate()
        results.push({ prop, valid: true })
      } catch (error) {
        results.push({ prop, valid: false, error })
      }
    }
  }
  
  const hasError = results.some(result => !result.valid)
  if (hasError) {
    const firstError = results.find(result => !result.valid)
    throw new Error(firstError.error || '表单验证失败')
  }
  
  return true
}

// 重置表单
const resetFields = () => {
  for (const [prop, item] of formItems) {
    if (item.reset) {
      item.reset()
    }
  }
}

// 清除验证
const clearValidate = (props) => {
  if (props) {
    const propList = Array.isArray(props) ? props : [props]
    propList.forEach(prop => {
      const item = formItems.get(prop)
      if (item && item.clearValidate) {
        item.clearValidate()
      }
    })
  } else {
    for (const [prop, item] of formItems) {
      if (item.clearValidate) {
        item.clearValidate()
      }
    }
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
  validateField,
  resetFields,
  clearValidate
})
</script>

<style scoped>
.custom-form {
  width: 100%;
}

.form-disabled {
  opacity: 0.6;
  pointer-events: none;
}
</style>
