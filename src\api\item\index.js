import request from '@/utils/request'

// api地址
const api = {
    // 服务类型
    serveType: '/api/v1/consumer/item/enum/serveType',
    // 套餐分页
    departmentList: '/api/v1/consumer/item/paging',
}


// 服务类型枚举
export function getServeTypeMap() {
    return request.post(api.serveType)
}

// 套餐分页查询
export function getItemPaging(serverType, orgId, page = 1, pageSize = 10) {
    let data = {
        serverType: serverType, // 套餐类型，0其他 1住院陪护
        orgId: orgId,  // 机构ID
        pageIndex: pageIndex,   // 页码，从1开始
        pageSize: pageSize  // 每页大小
    }
    return request.post(api.departmentList)
}