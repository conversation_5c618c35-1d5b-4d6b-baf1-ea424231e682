<template>
  <view class="custom-tab-bar">
    <view
      v-for="(item, index) in tabList"
      :key="index"
      class="tab-item"
      :class="{ active: currentIndex === index }"
      @click="switchTab(index)"
    >
      <view class="tab-icon-wrapper">
        <image
          :src="currentIndex === index ? item.selectedIconPath : item.iconPath"
          class="tab-icon"
        />
        <!-- 数字角标 -->
        <view
          v-if="item.badge && item.badge > 0"
          class="badge"
          :class="{ 'badge-dot': item.badge === true }"
        >
          <text v-if="item.badge !== true" class="badge-text">
            {{ item.badge > 99 ? '99+' : item.badge }}
          </text>
        </view>
      </view>
      <text class="tab-text" :class="{ active: currentIndex === index }">
        {{ item.text }}
      </text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useTabBarStore } from '@/stores/tabBar'

const props = defineProps({
  current: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['change'])

const tabBarStore = useTabBarStore()
const currentIndex = ref(props.current)

// tabBar 配置列表
const tabList = computed(() => [
  {
    pagePath: "pages/index/index",
    text: "首页",
    iconPath: "/static/tabbar/home.png",
    selectedIconPath: "/static/tabbar/home-active.png",
    badge: 0
  },
  {
    pagePath: "pages/order/index",
    text: "订单",
    iconPath: "/static/tabbar/sales.png",
    selectedIconPath: "/static/tabbar/sales-active.png",
    badge: tabBarStore.taskBadge
  },
  {
    pagePath: "pages/mine/index",
    text: "我的",
    iconPath: "/static/tabbar/mine.png",
    selectedIconPath: "/static/tabbar/mine-active.png",
    badge: tabBarStore.mineBadge
  }
])

// 更新当前索引的方法
const updateCurrentIndex = () => {
  try {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route

      console.log('当前页面路径:', currentRoute)

      // 查找对应的 tab 索引 - 更精确的匹配逻辑
      const index = tabList.value.findIndex(item => {
        const tabPath = item.pagePath.replace(/^\/+/, '')  // 去除开头的斜杠
        const routePath = currentRoute.replace(/^\/+/, '')  // 去除开头的斜杠

        // 精确匹配
        if (tabPath === routePath) {
          return true
        }

        // 包含匹配（处理子路径的情况）
        if (routePath.includes(tabPath) || tabPath.includes(routePath)) {
          return true
        }

        return false
      })

      if (index !== -1 && index !== currentIndex.value) {
        console.log('TabBar 索引更新:', currentIndex.value, '->', index)
        currentIndex.value = index
      }
    }
  } catch (error) {
    console.error('更新 TabBar 索引失败:', error)
  }
}

// 切换 tab
const switchTab = (index) => {
  if (currentIndex.value === index) return

  const oldIndex = currentIndex.value
  currentIndex.value = index
  const targetPage = tabList.value[index].pagePath

  console.log('TabBar 切换:', oldIndex, '->', index, targetPage)

  // 发射事件给父组件
  emit('change', { index, pagePath: targetPage })

  // 跳转页面 - 使用更兼容的方式
  uni.reLaunch({
    url: `/${targetPage}`,
    success: () => {
      console.log('reLaunch success to:', targetPage)
      // 延迟一下再检查，确保页面已经切换完成
      setTimeout(() => {
        updateCurrentIndex()
      }, 100)
    },
    fail: (err) => {
      // 如果 switchTab 失败，使用 reLaunch
      uni.redirectTo({
        url: `/${targetPage}`,
        success: () => {
          console.log('redirectTo success to:', targetPage)
          setTimeout(() => {
            updateCurrentIndex()
          }, 100)
        },
        fail: (reLaunchErr) => {
          console.error('Both switchTab and reLaunch failed:', reLaunchErr)
          // 最后尝试 navigateTo
          uni.navigateTo({
            url: `/${targetPage}`,
            success: () => {
              setTimeout(() => {
                updateCurrentIndex()
              }, 100)
            },
            fail: (navErr) => {
              console.error('All navigation methods failed:', navErr)
              // 还原索引
              currentIndex.value = oldIndex
            }
          })
        }
      })
    }
  })
}

// 监听 props 变化
watch(
  () => props.current,
  (newValue) => {
    if (newValue !== currentIndex.value) {
      currentIndex.value = newValue
    }
  },
  { immediate: true }
)

// 页面显示时更新索引
const onPageShow = () => {
  console.log('页面显示，检查 TabBar 状态')
  nextTick(() => {
    updateCurrentIndex()
  })
}

// 初始化和页面变化监听
onMounted(() => {
  console.log('CustomTabBar mounted')

  // 初始检查
  updateCurrentIndex()

  // 监听页面显示事件
  uni.onTabBarMidButton && uni.onTabBarMidButton(() => {
    updateCurrentIndex()
  })

  // 设置定时检查（防止遗漏）
  const checkInterval = setInterval(() => {
    checkPageChange()
  }, 500) // 每0.5秒检查一次，提高响应速度

  // 组件销毁时清除定时器
  onBeforeUnmount(() => {
    clearInterval(checkInterval)
  })
})

// 监听路由变化（通过页面栈变化检测）
let lastPageStackLength = 0
const checkPageChange = () => {
  const pages = getCurrentPages()
  const currentLength = pages.length

  // 页面栈长度变化或当前页面变化，说明发生了路由切换
  if (currentLength !== lastPageStackLength) {
    lastPageStackLength = currentLength
    updateCurrentIndex()
  }
}

// 暴露方法给父组件
defineExpose({
  setCurrentIndex: (index) => {
    if (index !== currentIndex.value) {
      console.log('外部设置 TabBar 索引:', currentIndex.value, '->', index)
      currentIndex.value = index
    }
  },
  getCurrentIndex: () => currentIndex.value,
  updateCurrentIndex
})
</script>

<style lang="scss" scoped>
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #ffffff;
  border-top: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4px 0;
    position: relative;

    .tab-icon-wrapper {
      position: relative;
      margin-bottom: 2px;

      .tab-icon {
        width: 24px;
        height: 24px;
      }

      .badge {
        position: absolute;
        top: -6px;
        right: -8px;
        min-width: 16px;
        height: 16px;
        background-color: #ff4757;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #ffffff;

        &.badge-dot {
          width: 8px;
          height: 8px;
          min-width: 8px;
          border-radius: 4px;
          top: -2px;
          right: -2px;
        }

        .badge-text {
          color: #ffffff;
          font-size: 10px;
          font-weight: 500;
          line-height: 1;
          padding: 0 4px;
          transform: scale(0.9);
        }
      }
    }

    .tab-text {
      font-size: 10px;
      color: #999999;
      line-height: 1;

      &.active {
        color: #007AFF;
        font-weight: 500;
      }
    }

    &.active {
      .tab-text {
        color: #007AFF;
      }
    }
  }
}

/* 适配不同设备 */
@media screen and (max-width: 375px) {
  .custom-tab-bar {
    .tab-item {
      .tab-icon-wrapper {
        .tab-icon {
          width: 22px;
          height: 22px;
        }
      }

      .tab-text {
        font-size: 9px;
      }
    }
  }
}
</style>
