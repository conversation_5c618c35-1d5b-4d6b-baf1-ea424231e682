<template>
  <view class="image-uploader">
    <!-- 标题 -->
    <view class="uploader-title" v-if="title">
      <text v-if="required" class="required">*</text>
      <text>{{ title }}</text>
    </view>

    <!-- 图片列表 -->
    <view class="image-list">
      <!-- 已上传的图片 -->
      <view class="image-item" v-for="(item, index) in imageList" :key="index">
        <image :src="item.url || item.path" class="image-preview" mode="aspectFill" @click="previewImage(index)"/>

        <!-- 上传状态遮罩 -->
        <view class="upload-mask" v-if="item.status === 'uploading'">
          <view class="loading-icon">
            <text class="loading-text">上传中...</text>
          </view>
        </view>

        <!-- 上传失败遮罩 -->
        <view class="upload-mask error-mask" v-if="item.status === 'error'">
          <view class="error-icon">
            <text class="error-text">上传失败</text>
          </view>
        </view>

        <!-- 删除按钮 -->
        <view class="delete-btn" v-if="!disabled && item.status !== 'uploading'" @click.stop="deleteImage(index)">
          <text class="delete-icon">×</text>
        </view>
      </view>

      <!-- 添加按钮 -->
      <view class="add-btn" v-if="!disabled && imageList.length < maxCount" @click="chooseImage">
        <text class="add-icon">+</text>
        <text class="add-text">添加图片</text>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tip-text" v-if="showTip">
      <text>最多可上传{{ maxCount }}张图片</text>
    </view>
  </view>
</template>

<script setup>
import {ref, computed, watch, nextTick} from 'vue'
import * as uploadApi from '@/api/upload'

// Props定义
const props = defineProps({
  // 图片列表
  modelValue: {
    type: Array,
    default: () => []
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 最大上传数量
  maxCount: {
    type: Number,
    default: 9
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否必填
  required: {
    type: Boolean,
    default: false
  },
  // 是否显示提示
  showTip: {
    type: Boolean,
    default: true
  },
  // 图片质量 (0-100)
  quality: {
    type: Number,
    default: 80
  },
  // 图片来源类型
  sourceType: {
    type: Array,
    default: () => ['camera']    // 'album', 'camera'
  },
  // 图片尺寸类型
  sizeType: {
    type: Array,
    default: () => ['compressed']  //  original 原图，compressed 压缩图
  }
})

// Emits定义
const emit = defineEmits(['update:modelValue', 'change', 'upload-success', 'upload-error'])

// 内部图片列表 - 使用计算属性避免双向绑定问题
const imageList = computed({
  get() {
    return props.modelValue || []
  },
  set(newValue) {
    emit('update:modelValue', newValue)
    emit('change', newValue)
  }
})

// 更新图片列表的辅助函数
const updateImageList = (newList) => {
  nextTick(() => {
    imageList.value = newList
  })
}

// 选择图片
const chooseImage = () => {
  const remainCount = props.maxCount - imageList.value.length

  uni.chooseImage({
    count: remainCount,
    sizeType: props.sizeType,
    sourceType: props.sourceType,
    success: (res) => {
      const tempFilePaths = res.tempFilePaths
      uploadImages(tempFilePaths)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 上传图片
const uploadImages = async (filePaths) => {
  const currentList = [...imageList.value]
  const startIndex = currentList.length

  // 添加到列表，设置为上传中状态
  filePaths.forEach(path => {
    currentList.push({
      path: path,
      url: '',
      status: 'uploading'
    })
  })

  // 更新列表
  updateImageList(currentList)

  // 逐个上传
  for (let i = 0; i < filePaths.length; i++) {
    const currentIndex = startIndex + i
    const filePath = filePaths[i]

    try {
      console.log('开始上传图片:', filePath)
      const result = await uploadApi.uploadFile(filePath)
      console.log('上传结果11:', result)

      if (result && result[0].success) {
        // 上传成功
        const fileUrl = result[0].data.fileUrl
        console.log('上传成功，文件URL:', fileUrl)

        const updatedList = [...imageList.value]
        updatedList[currentIndex] = {
          ...updatedList[currentIndex],
          url: fileUrl,
          status: 'success'
        }
        updateImageList(updatedList)

        emit('upload-success', {
          index: currentIndex,
          url: fileUrl,
          item: updatedList[currentIndex]
        })

      } else {
        // 上传失败
        console.error('上传失败，返回结果:', result)
        const errorMsg = result?.errMessage || result?.message || '上传失败'

        const updatedList = [...imageList.value]
        updatedList[currentIndex].status = 'error'
        updateImageList(updatedList)

        emit('upload-error', {
          index: currentIndex,
          error: errorMsg,
          item: updatedList[currentIndex]
        })

        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2500
        })

        // 2秒后移除失败的图片
        setTimeout(() => {
          const currentList = [...imageList.value]
          const errorIndex = currentList.findIndex(
              item => item.path === filePath && item.status === 'error'
          )
          if (errorIndex !== -1) {
            currentList.splice(errorIndex, 1)
            updateImageList(currentList)
          }
        }, 2000)
      }
    } catch (error) {
      console.error('上传图片异常:', error)
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })

      const updatedList = [...imageList.value]
      updatedList[currentIndex].status = 'error'
      updateImageList(updatedList)

      const errorMsg = error.message || '上传异常'

      emit('upload-error', {
        index: currentIndex,
        error: errorMsg,
        item: updatedList[currentIndex]
      })

      uni.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2500
      })

      // 1秒后移除失败的图片
      setTimeout(() => {
        const currentList = [...imageList.value]
        const errorIndex = currentList.findIndex(
            item => item.path === filePath && item.status === 'error'
        )
        if (errorIndex !== -1) {
          currentList.splice(errorIndex, 1)
          updateImageList(currentList)
        }
      }, 1000)
    }
  }
}

// 删除图片
const deleteImage = (index) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除这张图片吗？',
    success: (res) => {
      if (res.confirm) {
        const updatedList = [...imageList.value]
        updatedList.splice(index, 1)
        updateImageList(updatedList)
      }
    }
  })
}

// 预览图片
const previewImage = (index) => {
  const urls = imageList.value
      .filter(item => item.status === 'success' && item.url)
      .map(item => item.url)

  if (urls.length === 0) return

  const currentUrl = imageList.value[index].url
  const currentIndex = urls.indexOf(currentUrl)

  uni.previewImage({
    urls: urls,
    current: currentIndex >= 0 ? currentIndex : 0
  })
}
</script>

<style scoped>
.image-uploader {
  width: 100%;
  box-sizing: border-box;
}

.uploader-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
  word-wrap: break-word;
}

.required {
  color: #f56c6c;
  margin-right: 8rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.image-item {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.image-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
}

.error-mask {
  background-color: rgba(245, 108, 108, 0.8);
}

.loading-icon,
.error-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-text,
.error-text {
  color: white;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #f56c6c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.delete-icon {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1;
}

.add-btn {
  width: 180rpx;
  height: 180rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  flex-shrink: 0;
}

.add-icon {
  font-size: 50rpx;
  color: #999;
  line-height: 1;
}

.add-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  text-align: center;
}

.tip-text {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
  word-wrap: break-word;
}
</style>
