<template>
	<view>首页</view>

	    <!-- 自定义 TabBar -->
    <CustomTabBar ref="customTabBar"/>
</template>

<script setup>
import { onLoad,onShow} from '@dcloudio/uni-app'
import { onMounted, getCurrentInstance } from 'vue'
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import tabBarManager from '@/utils/tabBar'

onMounted(() => {
  // 初始化 tabBarManager
  tabBarManager.init()
})

onShow(() => {
  // 更新 TabBar 状态
  updateTabBarIndex()
})

// 页面显示时更新 TabBar 状态
const updateTabBarIndex = () => {
  // 通过 ref 获取 CustomTabBar 组件实例并更新索引
  const tabBarComponent = getCurrentInstance()?.refs?.customTabBar
  if (tabBarComponent && tabBarComponent.setCurrentIndex) {
    tabBarComponent.setCurrentIndex(1)
  }
}

</script>

<style>
</style>
