 import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useTabBarStore = defineStore('tabBar', () => {
  // 各个 tab 的角标数量
  const taskBadge = ref(0)        // 巡查 tab 角标
  const publishBadge = ref(0)     // 整改 tab 角标
  const createBadge = ref(0)      // 发布 tab 角标
  const mineBadge = ref(0)        // 我的 tab 角标

  // 设置巡查 tab 角标
  const setTaskBadge = (count) => {
    taskBadge.value = count
  }

  // 设置整改 tab 角标
  const setPublishBadge = (count) => {
    publishBadge.value = count
  }

  // 设置发布 tab 角标
  const setCreateBadge = (count) => {
    createBadge.value = count
  }

  // 设置我的 tab 角标
  const setMineBadge = (count) => {
    mineBadge.value = count
  }

  // 清除指定 tab 的角标
  const clearBadge = (tabName) => {
    switch (tabName) {
      case 'task':
        taskBadge.value = 0
        break
      case 'publish':
        publishBadge.value = 0
        break
      case 'create':
        createBadge.value = 0
        break
      case 'mine':
        mineBadge.value = 0
        break
    }
  }

  // 清除所有角标
  const clearAllBadges = () => {
    taskBadge.value = 0
    publishBadge.value = 0
    createBadge.value = 0
    mineBadge.value = 0
  }

  // 增加指定 tab 的角标数量
  const incrementBadge = (tabName, count = 1) => {
    switch (tabName) {
      case 'task':
        taskBadge.value += count
        break
      case 'publish':
        publishBadge.value += count
        break
      case 'create':
        createBadge.value += count
        break
      case 'mine':
        mineBadge.value += count
        break
    }
  }

  // 减少指定 tab 的角标数量
  const decrementBadge = (tabName, count = 1) => {
    switch (tabName) {
      case 'task':
        taskBadge.value = Math.max(0, taskBadge.value - count)
        break
      case 'publish':
        publishBadge.value = Math.max(0, publishBadge.value - count)
        break
      case 'create':
        createBadge.value = Math.max(0, createBadge.value - count)
        break
      case 'mine':
        mineBadge.value = Math.max(0, mineBadge.value - count)
        break
    }
  }

  // 获取总角标数量
  const getTotalBadgeCount = () => {
    return taskBadge.value + publishBadge.value + createBadge.value + mineBadge.value
  }

  return {
    // 状态
    taskBadge,
    publishBadge,
    createBadge,
    mineBadge,

    // 方法
    setTaskBadge,
    setPublishBadge,
    setCreateBadge,
    setMineBadge,
    clearBadge,
    clearAllBadges,
    incrementBadge,
    decrementBadge,
    getTotalBadgeCount
  }
})
