<template>
  <view class="header">
	<image class="header-bg" src="/static/2.png"></image>

    <view class="user-info">
      <image
        class="avatar"
        :src="userInfo.avatarUrl || '/static/common/default-avatar.png'"
        @error="handleAvatarError"
      ></image>
      <view class="info" v-if="userInfo">
        <text class="name">{{ userInfo.nickname }}</text>
        <text class="role">{{ userInfo.phoneNumber }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import {useUserStore} from '@/stores/user'
import {ref} from "vue"

const store = useUserStore()

const userInfo = ref({})

// 处理头像加载失败
const handleAvatarError = () => {
  userInfo.value.headerImg = '/static/common/default-avatar.png'
}

// 默认获取用户信息（优先缓存）
store.fetchUserInfo().then(user => {
  userInfo.value = user

  console.log(userInfo.value)


})
</script>

<style scoped>
.header {
  position: relative;
  height: 160rpx;
  background: #fff;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.header-title {
  height: 200rpx;
  line-height: 200rpx;
  position: relative;
  z-index: 1;
  font-size: 26px;
  font-weight: bold;
  color: #fff;
  text-align: center;
}

.user-info {
  width: 700rpx;
  position: absolute;
  display: flex;
  align-items: center;
  padding: 8px 16px;
  bottom: 10rpx;
  box-sizing: border-box;
  left: 0;
  right: 0;
  margin: 0 auto;
}

.user-info .avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 12px;
  background-color: #f5f5f5;
}

.user-info .info {
  display: flex;
  flex-direction: column;
}

.user-info .name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.user-info .role {
  font-size: 12px;
  color: #666;
}
</style>
