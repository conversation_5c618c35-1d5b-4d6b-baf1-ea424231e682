import { defineStore } from 'pinia'
import * as <PERSON>gin<PERSON><PERSON> from '@/api/login'
import * as User<PERSON><PERSON> from '@/api/user'
import storage from '@/utils/storage'
import { ref, computed } from 'vue'

const ACCESS_TOKEN_NAME = 'AccessTokenName'
const ACCESS_TOKEN_VALUE = 'AccessTokenValue'
const USER_ID = 'userId'

// 用户
export const useUserStore = defineStore('user', () => {
    const tokenName = ref(storage.get(ACCESS_TOKEN_NAME))
    const token = ref(storage.get(ACCESS_TOKEN_VALUE) || '')
    const userId = ref(storage.get(USER_ID) || 0)
    const userInfo = ref(null)

    // 修改登录状态判断
    const isLogin = computed(() => {
        return !!token.value && userId.value !== 0
    })

    // 登录方法
    const login = async (username, password, hospitalId) => {
        try {
            const res = await LoginApi.login(username, password, hospitalId)
            console.log(res)
            if (res.code === 200) {
                // 保存到本地存储
                storage.set(ACCESS_TOKEN_NAME, res.data.tokenName)
                storage.set(ACCESS_TOKEN_VALUE, res.data.tokenValue)
                return res.data
            }
        } catch (error) {
            throw error
        }
    }

    // 微信登陆
    const wxLogin = async (code) => {
        try {
            const res = await LoginApi.wxLogin( code )
            console.log(res)

        } catch (error) {
            throw error
        }
    }

    // 测试登陆
    const textLogin = async () =>{
        try {
            const res = await LoginApi.loginTest()
            console.log(res)
        } catch (error) {
            throw error
        } 
    }

    // 获取用户信息
    const fetchUserInfo = async (force = false) => {
        try {
            if (!force && userInfo.value) {
                return userInfo.value
            }
            const result = await UserApi.getUserInfo()
            console.log(result)
            userInfo.value = result.data
            hospitalId.value = result.data.hospitalId || '' // 更新 hospitalId
            return userInfo.value
        } catch (error) {
            console.error("获取用户信息失败", error)
            throw error
        }
    }

    // 登出方法
    const logout = () => {
        if (userId.value > 0) {
            storage.remove(USER_ID)
            storage.remove(ACCESS_TOKEN)
            token.value = ''
            userId.value = 0
            userInfo.value = null
        }
    }

    // 删除账号
    const deleteAccount = async () => {
        try {
            const res = await UserApi.deleteAccount()
            // 清空缓存
            storage.remove(USER_ID)
            storage.remove(ACCESS_TOKEN)
            token.value = ''
            userId.value = 0
            userInfo.value = null
            return res
        } catch (error) {
            console.error("删除用户失败", error)
            throw error
        }
    }

    return {
        token,
        tokenName,
        userId,
        userInfo,
        isLogin,
        login,
        fetchUserInfo,
        logout,
        deleteAccount
    }

})
