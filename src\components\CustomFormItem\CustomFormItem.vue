<template>
  <view class="custom-form-item" :class="[
    `label-${labelPosition}`,
    { 
      'form-item-error': hasError,
      'form-item-required': isRequired
    }
  ]">
    <!-- 标签 -->
    <view 
      v-if="label" 
      class="form-item-label"
      :style="labelStyle"
    >
      <text class="label-text">{{ label }}</text>
      <text v-if="isRequired" class="required-mark">*</text>
    </view>
    
    <!-- 内容区域 -->
    <view class="form-item-content" :style="contentStyle">
      <slot></slot>
    </view>
    
    <!-- 错误信息 -->
    <view v-if="hasError && errorMessage" class="form-item-error-text">
      {{ errorMessage }}
    </view>
  </view>
</template>

<script setup>
import { computed, inject, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  prop: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  rules: {
    type: Array,
    default: () => []
  },
  labelWidth: {
    type: [String, Number],
    default: ''
  },
  labelPosition: {
    type: String,
    default: ''
  }
})

// 注入表单数据
const formContext = inject('customForm', {})

// 错误状态
const hasError = ref(false)
const errorMessage = ref('')

// 计算属性
const labelPosition = computed(() => props.labelPosition || formContext.labelPosition || 'left')
const labelWidth = computed(() => {
  const width = props.labelWidth || formContext.labelWidth || '200rpx'
  return typeof width === 'number' ? `${width}rpx` : width
})

const isRequired = computed(() => {
  if (props.required) return true
  
  const rules = props.rules.length ? props.rules : (formContext.rules?.[props.prop] || [])
  return rules.some(rule => rule.required)
})

// 标签样式
const labelStyle = computed(() => {
  const style = {}
  
  if (labelPosition.value === 'left' || labelPosition.value === 'right') {
    style.width = labelWidth.value
    style.flexShrink = 0
  }
  
  return style
})

// 内容样式
const contentStyle = computed(() => {
  const style = {}
  
  if (labelPosition.value === 'left' || labelPosition.value === 'right') {
    style.flex = 1
  }
  
  return style
})

// 获取字段值
const getFieldValue = () => {
  if (!props.prop || !formContext.model) return undefined
  return formContext.model[props.prop]
}

// 验证字段
const validate = async () => {
  if (!props.prop) return true
  
  const value = getFieldValue()
  const rules = props.rules.length ? props.rules : (formContext.rules?.[props.prop] || [])
  
  for (const rule of rules) {
    try {
      // 必填验证
      if (rule.required) {
        if (value === undefined || value === null) {
          throw new Error(rule.message || `${props.label}不能为空`)
        }
        // 对于字符串类型，检查是否为空字符串
        if (typeof value === 'string' && value === '') {
          throw new Error(rule.message || `${props.label}不能为空`)
        }
        // 对于数组类型，检查是否为空数组
        if (Array.isArray(value) && value.length === 0) {
          throw new Error(rule.message || `${props.label}不能为空`)
        }
      }
      
      // 最小长度验证
      if (rule.min && value && value.length < rule.min) {
        throw new Error(rule.message || `${props.label}长度不能少于${rule.min}个字符`)
      }
      
      // 最大长度验证
      if (rule.max && value && value.length > rule.max) {
        throw new Error(rule.message || `${props.label}长度不能超过${rule.max}个字符`)
      }
      
      // 正则验证
      if (rule.pattern && value && !rule.pattern.test(value)) {
        throw new Error(rule.message || `${props.label}格式不正确`)
      }
      
      // 自定义验证函数
      if (rule.validator && typeof rule.validator === 'function') {
        await rule.validator(rule, value)
      }
    } catch (error) {
      hasError.value = true
      errorMessage.value = error.message
      throw error
    }
  }
  
  hasError.value = false
  errorMessage.value = ''
  return true
}

// 重置字段
const reset = () => {
  hasError.value = false
  errorMessage.value = ''
}

// 清除验证
const clearValidate = () => {
  hasError.value = false
  errorMessage.value = ''
}

// 注册到表单
onMounted(() => {
  if (props.prop && formContext.registerItem) {
    formContext.registerItem(props.prop, {
      validate,
      reset,
      clearValidate
    })
  }
})

// 卸载时注销
onUnmounted(() => {
  if (props.prop && formContext.unregisterItem) {
    formContext.unregisterItem(props.prop)
  }
})

// 监听值变化，清除错误
watch(() => getFieldValue(), () => {
  if (hasError.value) {
    clearValidate()
  }
})
</script>

<style scoped>
.custom-form-item {
  margin-bottom: 30rpx;
  position: relative;
}

/* 标签位置 */
.label-left {
  display: flex;
  align-items: flex-start;
}

.label-top {
  display: flex;
  flex-direction: column;
}

.label-right {
  display: flex;
  align-items: flex-start;
  flex-direction: row-reverse;
}

/* 标签样式 */
.form-item-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-left .form-item-label {
  margin-bottom: 0;
  margin-right: 20rpx;
  padding-top: 20rpx;
}

.label-right .form-item-label {
  margin-bottom: 0;
  margin-left: 20rpx;
  padding-top: 20rpx;
}

.label-text {
  font-size: 28rpx;
  color: #000;
  font-weight: 500;
}

.required-mark {
  color: #ff4d4f;
  margin-left: 4rpx;
  font-size: 28rpx;
}

/* 内容区域 */
.form-item-content {
  position: relative;
}

/* 错误状态 */
.form-item-error .label-text {
  color: #ff4d4f;
}

.form-item-error-text {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .label-text {
    font-size: 30rpx;
  }
  
  .required-mark {
    font-size: 30rpx;
  }
  
  .form-item-error-text {
    font-size: 26rpx;
  }
}
</style>
